import type { CallOptions, ClientDuplexStream, Metadata, StatusObject } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { DuplexStreamWrapper, type DuplexStreamWrapperOptions } from '../../../streams/duplex-stream-wrapper'
import { EMPTY_METADATA } from '../../../utils'
import type { GeyserClient, SubscribeRequest, SubscribeUpdate } from '../generated/geyser'
import { resolveHeartbeatOptions } from './options'
import { createPingMessage, isPingRequestMessage } from './requests'
import { isPingResponseMessage } from './responses'

export type YellowstoneGeyserSubscribeStream = ClientDuplexStream<SubscribeRequest, SubscribeUpdate>

export interface YellowstoneGeyserStreamWrapperOptions extends DuplexStreamWrapperOptions<SubscribeRequest> {
    metadata?: Metadata
    callOptions?: CallOptions
    sendLastRequestOnResubscribed?: boolean
    emitPingResponse?: boolean
    updateOnPing?: boolean
}

export class YellowstoneGeyserStreamWrapper extends DuplexStreamWrapper<SubscribeRequest, SubscribeUpdate> {
    protected readonly sendLastRequestOnResubscribed: boolean
    protected readonly emitPingResponse: boolean
    protected readonly updateOnPing: boolean

    protected lastRequest?: SubscribeRequest

    public constructor(protected readonly client: GeyserClient, { metadata = EMPTY_METADATA, callOptions, heartbeat = true, sendLastRequestOnResubscribed = true, emitPingResponse = false, updateOnPing = true, ...options }: YellowstoneGeyserStreamWrapperOptions = {}) {
        super(() => this.client.subscribe(metadata, callOptions), { heartbeat: resolveHeartbeatOptions(heartbeat), ...options })

        this.sendLastRequestOnResubscribed = sendLastRequestOnResubscribed
        this.emitPingResponse = emitPingResponse
        this.updateOnPing = updateOnPing

        this.on('wrote', this.handleWrote.bind(this))
        this.resubscriber.onResubscribed(this.handleResubscribed.bind(this))
    }

    public update() {
        if (notNullish(this.lastRequest)) {
            return this.write(this.lastRequest)
        }
    }

    public ping(id?: number) {
        return this.write(createPingMessage(id))
    }

    protected override handleClose(stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>, status?: StatusObject) {
        if (this.isExplicitlyClosed) {
            this.lastRequest = undefined
        }

        return super.handleClose(stream, status)
    }

    protected override emitData(data: SubscribeUpdate) {
        if (isPingResponseMessage(data)) {
            if (this.updateOnPing) {
                this.update()
            }

            if (!this.emitPingResponse) {
                return
            }
        }

        super.emitData(data)
    }

    protected handleWrote(data: SubscribeRequest) {
        if (!isPingRequestMessage(data)) {
            this.lastRequest = data
        }
    }

    protected handleResubscribed() {
        if (this.sendLastRequestOnResubscribed) {
            this.update()
        }
    }
}
